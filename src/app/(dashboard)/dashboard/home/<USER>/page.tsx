"use client";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import axios from "axios";
import { <PERSON>, <PERSON>rop<PERSON> } from "next/font/google";
import Image from "next/image";
import { ChangeEvent, useEffect, useRef, useState } from "react";
import { Im<PERSON>ross } from "react-icons/im";
import { TbCaptureFilled } from "react-icons/tb";

type BoxConfig = {
    title:string | null,
    imgpath:File | string | null,
    colstart: string | null,
    colspan : string | null
}

// type BoxConfig = {
//     title:string | null,
//     currentImg: File | null,
//     previousImg: string | null,
//     colstart: string | null,
//     colspan : string | null
// }

type ImgPathConfig = {
    currentImg:File | null,
    previousImg: string | null
}

const anton = Anton({
    subsets:['latin'],
    weight:'400'
})

const manrope = Manrope({
    subsets:['latin']
})

export default function Dresstyle(){
    const [toggleOpt,setToggleOpt] = useState(false);
    const [toggleCategory,setToggleCategory] = useState<number | null>(null);
    const [boxConfig,setBoxConfig] = useState<BoxConfig[]>([]);
    const colStartRef = useRef<HTMLSelectElement | null>(null);
    const colSpanRef = useRef<HTMLSelectElement | null>(null);
    const [addOrUpdate,setAddUpdate] = useState<boolean>(false);
    const queryClient = useQueryClient();

    const {isLoading,isError,data} = useQuery({
        queryKey:["getDresstyleData"],
        queryFn:async ()=>{
            const data = await axios.get("/api/getdresstyledata");
            const result:BoxConfig[] = data.data;

            setBoxConfig(result);

            return result;
        }
    })

    const uploadData = useMutation<FormData,unknown,FormData>({
        mutationFn:async(formData)=>{
            // return axios.post("/api/dresstyledata",formData)
            // .then(res=>console.log(res.data));
            const post = await axios.post("/api/dresstyledata",formData);
            const result = post.data;

            console.log(result);

            return result;
        },

        onSuccess:()=>{queryClient.invalidateQueries({queryKey:["getDresstyleData"]})}
    })

    const addColumn=()=>{
        const wrap = {
            title: null,
            imgpath:null,
            colstart: colStartRef.current?.value ?? "col-start-1",
            colspan : colSpanRef.current?.value ?? "col-span-1"
        }

        // const wrap = {
        //     title: null,
        //     currentImg:null,
        //     previousImg:null,
        //     colstart: colStartRef.current?.value ?? "col-start-1",
        //     colspan : colSpanRef.current?.value ?? "col-span-1"
        // }

        setBoxConfig([...boxConfig,wrap])
    }

    const inputChange=(event: ChangeEvent<HTMLInputElement>,index:number,colstart:string|null,colspan:string|null)=>{
        const {name,value,files} = event.target;
        let copy = boxConfig[index];

        // const wrap = {
        //     ...copy,
        //     title:name === "title"?value : copy.title,
        //     imgpath:name === "imgpath"? {...copy.imgpath,currentImg:files?.[0] ?? null} : copy.imgpath,
        //     colspan,
        //     colstart
        // }

        const wrap = {
            ...copy,
            title:name === "title"?value : copy.title,
            imgpath:name === "imgpath"? files?.[0] ?? null : null,
            colspan,
            colstart
        }

        setBoxConfig(prev=>prev.map((items,serial)=>(serial === index? wrap : items)));
    }

    const removeBox=(serial:number)=>{
        const copy = boxConfig;
        const update = copy.filter((items,index)=> index !== serial);

        setBoxConfig(update);
    }

    const addData=()=>{
        const formData = new FormData();

        boxConfig.forEach((items,index)=>{
            (["title","imgpath","colstart","colspan"] as (keyof BoxConfig)[]).map((props)=>{
                formData.append(`${props}[${index}]`,items[props] as string)
            })
        })

        uploadData.mutate(formData)
    }
    
    const updateData=()=>{
        console.log("click from update data")
    }
    useEffect(()=>{
        if(data && data.length > 0){
            setAddUpdate(true)
        }

        console.log(data);

    },[data])
    return(
        <>
        <section className="mt-20 px-5 pb-20">
            <div className="mb-10">
                <h2 className={`${anton.className} capitalize text-5xl font-semibold`} style={{WebkitTextStroke:'2px black',WebkitTextFillColor:"transparent"}}>
                    show dress style
                </h2>
            </div>
            {
                toggleOpt?
                <div className="mb-5 flex flex-row gap-x-5 items-center">
                <div className="bg-white py-1 px-5 shadow-[2px_2px_3px_black]/50 rounded-full">
                    <select name="colStart" className={`px-5 border-b border-black/20 rounded-lg focus:outline-none ${manrope.className} capitalize tracking-wider font-medium`} ref={colStartRef} defaultValue={"col-start-1"}>
                        <option value="col-start-1">col-start-1</option>
                        <option value="col-start-2">col-start-2</option>
                        <option value="col-start-3">col-start-3</option>
                    </select>
                </div>

                <div className="bg-white py-1 px-5 shadow-[2px_2px_3px_black]/50 rounded-full">
                    <select name="colSpan" className={`px-5 border-b border-black/20 rounded-lg focus:outline-none ${manrope.className} capitalize tracking-wider font-medium`} ref={colSpanRef} defaultValue={"col-span-1"}>
                        <option value="col-span-1">col span 1</option>
                        <option value="col-span-2">col span 2</option>
                        <option value="col-span-3">col span 3</option>
                    </select>
                </div>

                <div className="flex flex-row gap-x-4 items-center">
                    <button className={`${manrope.className} bg-[#636e72] px-3 py-1 rounded-full text-white shadow-[5px_3px_3px_black] transtion-all duration-150 ease-linear hover:shadow-[1px_0px_1px_black] active:scale-95 active:duration-75`} onClick={addColumn}>
                        add column
                    </button>

                    <button className={`${manrope.className} bg-[#d63031] px-3 py-1 rounded-full text-white shadow-[5px_3px_3px_#d63031]/50 transtion-all duration-150 ease-linear hover:shadow-[1px_0px_1px_#d63031]/50 active:scale-95 active:duration-75`} onClick={()=>{setToggleOpt(!toggleOpt)}}>
                        cancel
                    </button>
                </div>
            </div>:
            <div className="mb-5">
                <button className={`${manrope.className} font-semibold shadow-[5px_0px_3px_black] px-4 py-2 rounded-full transition-all duration-200 ease-linear hover:shadow-none hover:bg-black/10 hover:text-black active:scale-95 active:duration-100`} onClick={()=>{setToggleOpt(!toggleOpt)}}>
                    add column
                </button>
            </div>
            }
            
            <div className="grid grid-cols-3 gap-x-5 gap-y-10 px-5 w-full py-10 border border-gray-500/30 justify-between rounded-lg">
                {
                    boxConfig.map((items,index)=>{
                        return <div key={index}  className={`${items.colstart} ${items.colspan}`}>
                            <div className="mb-5 w-[80%] bg-white py-1 px-2 shadow-md shadow-black/20 rounded-xl relative">
                                <div className="h-10 w-full border border-black/20 rounded-lg" onClick={()=>{
                                    if(toggleCategory === index){
                                        setToggleCategory(null)
                                    }else{
                                        setToggleCategory(index);
                                    }
                                }}>
                                    <input type="text" name="title" id=""  className={`h-full w-full focus:outline-none px-2 text-[#34495e] ${manrope.className} font-medium`} value={items.title || ""} onChange={(event)=>{
                                        setToggleCategory(null)
                                        inputChange(event,index,items.colstart,items.colspan)
                                        }}/>
                                </div>
                                {
                                    toggleCategory === index?
                                    <div className="w-full rounded-xl absolute left-0 py-2">
                                    <div className="bg-white w-full py-5 shadow-lg shadow-black/20 rounded-xl flex flex-col px-2">
                                        <div className="text-center border border-black/20 rounded-lg py-2">
                                            <p className={`${manrope.className} font-medium text-[#95a5a6] capitalize`}>
                                                no item founded
                                            </p>
                                        </div>
                                    </div>
                                </div>:null
                                }
                            </div>

                            <div className="w-full h-[289px] relative rounded-lg border border-black/20 mb-3">

                            <div className="absolute right-0 top-[-20%] group">
                                <span className="text-rose-200 transition-all ease-linear duration-150 group-hover:text-rose-500 group-hover:cursor-pointer" onClick={()=>{removeBox(index)}}>
                                    <ImCross />
                                </span>
                            </div>
                                {
                                    items.imgpath?
                                    <>
                                    <label htmlFor={`selectImg${index}`} className="absolute h-full w-full flex justify-center items-center">

                                        <input type="file" name="imgpath" id={`selectImg${index}`} className="hidden" onChange={(event)=>{inputChange(event,index,items.colstart,items.colspan)}}/>

                                        <Image src={items.imgpath instanceof File ? URL.createObjectURL(items.imgpath): `/assets/${items.imgpath}`} alt="dressImage" fill className="h-full w-full absolute top-0 left-0 rounded-lg"/>

                                        <TbCaptureFilled className="text-6xl text-[#34495e] z-10"/>
                                    </label>
                                    </>
                                    :
                                    <label htmlFor={`selectImg${index}`} className="absolute h-full w-full flex justify-center items-center">
                                    <input type="file" name="imgpath" id={`selectImg${index}`} className="hidden" onChange={(event)=>{inputChange(event,index,items.colstart,items.colspan)}}/>

                                    <TbCaptureFilled className="text-6xl text-[#95a5a6]"/>
                                </label>
                                }
                            </div>
                        </div>
                        
                    })
                }
            </div>

            <div className="w-full flex flex-row justify-end gap-x-5 mt-12 mb-8">
                {
                    addOrUpdate?
                    <button className={`${manrope.className} bg-[#1abc9c] px-5 py-2 rounded-full text-white text-lg font-medium shadow-[5px_3px_3px_#1abc9c]/50 transtion-all duration-150 ease-linear hover:shadow-[1px_0px_1px_#1abc9c] active:scale-95 active:duration-75 hover:cursor-pointer`} onClick={updateData}>
                    Update
                    </button>:
                    <button className={`${manrope.className} bg-[#3498db] px-5 py-2 rounded-full text-white text-lg font-medium shadow-[5px_3px_3px_#3498db]/50 transtion-all duration-150 ease-linear hover:shadow-[1px_0px_1px_#3498db] active:scale-95 active:duration-75 hover:cursor-pointer`} onClick={addData}>
                    Add
                    </button>
                }
                <button className={`${manrope.className} bg-[#e74c3c] px-5 py-2 rounded-full text-white text-lg font-medium shadow-[5px_3px_3px_#e74c3c]/50 transtion-all duration-150 ease-linear hover:shadow-[1px_0px_1px_#e74c3c] active:scale-95 active:duration-75 hover:cursor-pointer`} onClick={()=>{setBoxConfig([])}}>
                    Clear
                </button>
            </div>
        </section>
        </>
    )
}