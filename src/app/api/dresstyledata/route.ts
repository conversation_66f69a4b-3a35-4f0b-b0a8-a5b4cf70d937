import { mkdir, writeFile, unlink } from "fs/promises";
import { NextRequest, NextResponse } from "next/server";
import path from "path";
import fs from "fs";
import { PrismaClient } from "../../../../generated/prisma";

type Obj = {
    title: string | null,
    imgpath: ImageConfig,
    colstart: string | null,
    colspan : string | null
}

type ImageConfig = {
    currentImg: string | File | null,
    previousImg: string | null
}

type IntermediateObj = {
    title?: FormDataEntryValue,
    currentImg?: FormDataEntryValue,
    previousImg?: FormDataEntryValue,
    colstart?: FormDataEntryValue,
    colspan?: FormDataEntryValue
}

const prisma = new PrismaClient();

export async function POST(req:NextRequest){
    const formData = await req.formData();

    
    // reformat formData to normal array of object  
    // const arr:[string,FormDataEntryValue][] = [...formData.entries()];
    
    // const arrObj = arr.reduce((acc,[key,value])=>{
    //     const match = key.match(/([a-zA-Z_]+)\[(\d+)\]/);

    //     if(!match) return acc;

    //     const [,field,indexStr] = match;
    //     const index = parseInt(indexStr, 10);

    //     if(!acc[index]){
    //         acc[index] = {} as IntermediateObj;
    //     }

    //     (acc[index] as any)[field] = value;

    //     return acc;
    // }, [] as IntermediateObj[]);
    // create the expected array of object
    // const collectArr = arrObj.map(({currentImg,previousImg,...rest})=>{
    //     return {
    //         ...rest,
    //         imgpath: {
    //             currentImg: currentImg instanceof File ? currentImg : (typeof currentImg === 'string' ? currentImg : null),
    //             previousImg: typeof previousImg === 'string' ? previousImg : null
    //         }
    //     } as Obj;
    // });

    // const reStructure =await Promise.all(collectArr.map(async ({imgpath,...rest},index)=>{
    //     if(imgpath.currentImg != "null"){
    //         const fileName = imgpath.currentImg instanceof File ? imgpath.currentImg.name: "";
    //         const separate = fileName.split(".");
    //         const imgName  = separate[0];
    //         const renameImg= imgName + Date.now();
    //         const addFormat= renameImg + "." + separate[1];
            
    //         // buffer file
    //         const arrayBuffer = imgpath.currentImg instanceof File ? await imgpath.currentImg.arrayBuffer() : null;

    //         const buffer = arrayBuffer ?  Buffer.from(arrayBuffer) : "";    

    //         // place it to public folder
    //         const uploadDir = path.join(process.cwd(),"public/assets/");

    //         // upload to pulic folder
    //         await mkdir(uploadDir,{recursive:true});

    //         await writeFile(path.join(process.cwd(),"public/assets/" + addFormat),buffer);
            
    //         // remove old img base on previousImg
    //         if(imgpath.previousImg !=null){
    //             fs.unlink("public/assets/" + imgpath.previousImg,(err)=>{console.log(err)})
    //         }
    //         return {...rest,imgpath:addFormat};
    //     }else{
    //         return {...rest,imgpath:imgpath.previousImg ||  null}
    //     }
    // })) 
    // const [title,currentImg,previousImg,colspan,colstart] = ["title","imgpath","colstart","colspan"].map(items=>formData.getAll(items));

    // try{

    //     await prisma.dresstyle.createMany({
    //         data:reStructure
    //     });

    //     return NextResponse.json({message:"added"});
    // }catch(error){
    //     return NextResponse.json(error)
    // }
}
